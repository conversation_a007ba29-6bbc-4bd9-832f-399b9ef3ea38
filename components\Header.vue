<template>
  <div class="bg-white w-full">
    <div class="bg-white h-[75px] w-full shadow-[0px_4px_2px_0px_rgba(0,0,0,0.1)] relative z-10">
      <div class="max-w-[1140px] mx-auto px-4 h-full flex items-center justify-between">
        <!-- Left side: Back button + Logo -->
        <div class="flex items-center gap-2">
          <!-- Back button -->
          <button class="w-6 h-6 flex items-center justify-center">
            <svg width="8" height="12" viewBox="0 0 8 12" fill="none">
              <path d="M7.41 1.41L6 0L0 6L6 12L7.41 10.59L2.83 6L7.41 1.41Z" fill="#000000"/>
            </svg>
          </button>
          
          <!-- Logo -->
          <div class="w-[130px] h-[31.75px]">
            <img alt="Logo BIC" class="w-full h-full object-contain" src="/assets/images/logo.svg" />
          </div>
        </div>
        
        <!-- Right side: Navigation -->
        <div class="flex items-center gap-4">
          <!-- Sản phẩm -->
          <div class="flex items-center justify-center px-0 py-[3px]">
            <span class="font-bold text-[16px] leading-[24px] text-[#0D68B2]">
              Sản phẩm
            </span>
          </div>

          <!-- Quản lý -->
          <div class="flex items-center justify-center px-0 py-[3px]">
            <span class="font-normal text-[16px] leading-[24px] text-[#34404B]">
              Quản lý
            </span>
          </div>
          
          <!-- Account Icon with Dropdown -->
          <div class="relative" ref="dropdownRef">
            <button
              @click="toggleDropdown"
              class="flex items-center justify-center p-2 rounded-full hover:bg-gray-100 transition-colors">
              <img
                src="/assets/images/account-circle-icon.svg"
                alt="Account"
                class="w-6 h-6"
              />
            </button>

            <!-- Dropdown Menu for Authenticated Users -->
            <div v-if="isDropdownOpen && authStore.isLoggedIn" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50">
              <div class="py-1">
                <NuxtLink to="/account/profile"
                  @click="closeDropdown"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                  Thông tin cá nhân
                </NuxtLink>
                <button
                  @click="handleOpenChangePasswordModal"
                  class="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                  Đổi mật khẩu
                </button>
                <hr class="my-1 border-gray-200">
                <button
                  @click="handleLogout"
                  class="w-full text-left block px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                  Đăng xuất
                </button>
              </div>
            </div>

            <!-- Dropdown Menu for Non-Authenticated Users -->
            <div v-if="isDropdownOpen && !authStore.isLoggedIn" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50">
              <div class="py-1">
                <button
                  @click="handleOpenLoginModal"
                  class="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                  Đăng nhập
                </button>
                <button
                  @click="handleOpenRegisterModal"
                  class="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                  Đăng ký
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Change Password Modal -->
    <ChangePasswordForm
      :is-open="showChangePasswordModal"
      @close="closeChangePasswordModal"
      @success="onPasswordChangeSuccess"
    />

    <!-- Auth Modals -->
    <LoginModal
      :is-open="showLoginModal"
      @close="closeLoginModal"
      @switch-to-register="switchToRegister"
      @switch-to-forgot-password="switchToForgotPassword"
      @login-success="onLoginSuccess"
    />

    <RegisterModal
      :is-open="showRegisterModal"
      @close="closeRegisterModal"
      @switch-to-login="switchToLogin"
      @register-success="onRegisterSuccess"
    />

    <ForgotPasswordModal
      :is-open="showForgotPasswordModal"
      @close="closeForgotPasswordModal"
      @switch-to-login="switchToLogin"
      @reset-success="onResetSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import ChangePasswordForm from '~/components/ChangePasswordForm.vue'
import LoginModal from '~/components/auth/LoginModal.vue'
import RegisterModal from '~/components/auth/RegisterModal.vue'
import ForgotPasswordModal from '~/components/auth/ForgotPasswordModal.vue'
import { useAuthStore } from '~/stores/auth'

// Auth store
const authStore = useAuthStore()

// Dropdown state
const isDropdownOpen = ref(false)
const dropdownRef = ref<HTMLElement | null>(null)

// Change password modal state
const showChangePasswordModal = ref(false)

// Auth modal states
const showLoginModal = ref(false)
const showRegisterModal = ref(false)
const showForgotPasswordModal = ref(false)

const openChangePasswordModal = () => {
  showChangePasswordModal.value = true
}

const closeChangePasswordModal = () => {
  showChangePasswordModal.value = false
}

const onPasswordChangeSuccess = () => {
  showChangePasswordModal.value = false
  console.log('Đổi mật khẩu thành công!')
}

// Dropdown functions
const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value
}

const closeDropdown = () => {
  isDropdownOpen.value = false
}

const handleOpenChangePasswordModal = () => {
  openChangePasswordModal()
  closeDropdown()
}

const handleLogout = () => {
  authStore.logout()
  closeDropdown()
}

// Auth modal handlers
const handleOpenLoginModal = () => {
  showLoginModal.value = true
  closeDropdown()
}

const handleOpenRegisterModal = () => {
  showRegisterModal.value = true
  closeDropdown()
}

const closeLoginModal = () => {
  showLoginModal.value = false
}

const closeRegisterModal = () => {
  showRegisterModal.value = false
}

const closeForgotPasswordModal = () => {
  showForgotPasswordModal.value = false
}

const switchToLogin = () => {
  showRegisterModal.value = false
  showForgotPasswordModal.value = false
  showLoginModal.value = true
}

const switchToRegister = () => {
  showLoginModal.value = false
  showForgotPasswordModal.value = false
  showRegisterModal.value = true
}

const switchToForgotPassword = () => {
  showLoginModal.value = false
  showRegisterModal.value = false
  showForgotPasswordModal.value = true
}

// Auth success handlers
const onLoginSuccess = (user: any) => {
  console.log('Login successful:', user)
  // Auth store already handles the login state
}

const onRegisterSuccess = (user: any) => {
  console.log('Registration successful:', user)
  // Switch to login modal after successful registration
}

const onResetSuccess = (email: string) => {
  console.log('Password reset sent to:', email)
}

// Close dropdown when clicking outside
onMounted(() => {
  const handleClickOutside = (event: Event) => {
    if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
      closeDropdown()
    }
  }
  
  document.addEventListener('click', handleClickOutside)
  
  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })
})
</script>

<style scoped>
/* Header styles */
</style>
