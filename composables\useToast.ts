import { useToast as useVueToastification } from "vue-toastification"

export const useToast = () => {
  const toast = useVueToastification()

  const showSuccess = (message: string, options?: any) => {
    toast.success(message, {
      timeout: 3000,
      ...options
    })
  }

  const showError = (message: string, options?: any) => {
    toast.error(message, {
      timeout: 5000,
      ...options
    })
  }

  const showInfo = (message: string, options?: any) => {
    toast.info(message, {
      timeout: 3000,
      ...options
    })
  }

  const showWarning = (message: string, options?: any) => {
    toast.warning(message, {
      timeout: 4000,
      ...options
    })
  }

  const clear = () => {
    toast.clear()
  }

  return {
    showSuccess,
    showError,
    showInfo,
    showWarning,
    clear,
    toast
  }
}
