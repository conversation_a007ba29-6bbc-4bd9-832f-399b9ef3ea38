// Simple, reliable toast system that works without external dependencies
const createToastElement = (message: string, type: 'success' | 'error' | 'info' | 'warning') => {
  if (import.meta.server) return

  const toast = document.createElement('div')
  toast.className = `
    fixed top-4 right-4 z-[9999] px-4 py-3 rounded-lg shadow-lg text-white font-medium max-w-sm
    transition-all duration-300 transform translate-x-full opacity-0 pointer-events-auto cursor-pointer
  `.trim()

  // Set colors based on type
  const colors = {
    success: 'bg-green-500 hover:bg-green-600',
    error: 'bg-red-500 hover:bg-red-600',
    warning: 'bg-yellow-500 hover:bg-yellow-600',
    info: 'bg-blue-500 hover:bg-blue-600'
  }

  toast.className += ` ${colors[type]}`
  toast.textContent = message

  // Add to DOM
  document.body.appendChild(toast)

  // Animate in
  requestAnimationFrame(() => {
    toast.className = toast.className.replace('translate-x-full opacity-0', 'translate-x-0 opacity-100')
  })

  // Auto remove
  const timeout = type === 'error' ? 5000 : 3000
  const removeToast = () => {
    toast.className = toast.className.replace('translate-x-0 opacity-100', 'translate-x-full opacity-0')
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast)
      }
    }, 300)
  }

  setTimeout(removeToast, timeout)

  // Click to dismiss
  toast.addEventListener('click', removeToast)
}

export const useToast = () => {
  console.log('useToast: Initializing simple toast system')

  const showSuccess = (message: string, _options?: any) => {
    console.log('Toast Success:', message)
    createToastElement(message, 'success')
  }

  const showError = (message: string, _options?: any) => {
    console.log('Toast Error:', message)
    createToastElement(message, 'error')
  }

  const showInfo = (message: string, _options?: any) => {
    console.log('Toast Info:', message)
    createToastElement(message, 'info')
  }

  const showWarning = (message: string, _options?: any) => {
    console.log('Toast Warning:', message)
    createToastElement(message, 'warning')
  }

  const clear = () => {
    // Remove all toasts
    if (import.meta.client) {
      const toasts = document.querySelectorAll('[class*="fixed top-4 right-4"]')
      toasts.forEach(toast => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast)
        }
      })
    }
  }

  return {
    showSuccess,
    showError,
    showInfo,
    showWarning,
    clear,
    toast: null
  }
}
