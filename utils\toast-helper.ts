// Helper to handle vue-toastification import issues
export const getToastInstance = () => {
  if (import.meta.server) {
    return null
  }

  try {
    // Try to get from Nuxt app
    const nuxtApp = useNuxtApp()
    const toast = nuxtApp.vueApp.config.globalProperties.$toast
    
    if (toast) {
      return toast
    }
    
    // If not available, return null
    return null
  } catch (error) {
    console.warn('Failed to get toast instance:', error)
    return null
  }
}

export const createToastMethods = (toast: any) => {
  if (!toast) {
    return {
      showSuccess: (msg: string) => console.log('Success:', msg),
      showError: (msg: string) => console.log('Error:', msg),
      showInfo: (msg: string) => console.log('Info:', msg),
      showWarning: (msg: string) => console.log('Warning:', msg),
      clear: () => {}
    }
  }

  return {
    showSuccess: (message: string, options?: any) => {
      toast.success?.(message, { timeout: 3000, ...options })
    },
    showError: (message: string, options?: any) => {
      toast.error?.(message, { timeout: 5000, ...options })
    },
    showInfo: (message: string, options?: any) => {
      toast.info?.(message, { timeout: 3000, ...options })
    },
    showWarning: (message: string, options?: any) => {
      toast.warning?.(message, { timeout: 4000, ...options })
    },
    clear: () => {
      toast.clear?.()
    }
  }
}
